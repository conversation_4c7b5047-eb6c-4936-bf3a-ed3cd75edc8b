"""
Streamlined in-memory registry for a specific database
"""
import asyncio
import os
import time
from typing import Dict, Optional, Any, Set, List, Tuple
from ...logging import get_logger

# Import specialized managers
from .cache_manager import CacheManager
from .addon_manager import AddonManager
from .model_metadata_manager import ModelMetadataManager
from .app_registry_route_manager import AppRegistryRouteManager


class AppRegistry:
    """
    Enhanced in-memory registry for a specific database
    Supports addon loading in installation order, model/field metadata, and query caching
    """

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.created_at = time.time()

        # Core data structures
        self.installed_modules: Dict[str, Dict[str, Any]] = {}  # Module name -> module info
        self.environments: Set[Any] = set()  # Track environments using this registry

        # Initialize specialized managers
        self.cache_manager = CacheManager(db_name)
        self.addon_manager = AddonManager(db_name)
        self.model_metadata_manager = ModelMetadataManager(db_name)
        self.route_manager = AppRegistryRouteManager(db_name, self.addon_manager)

        # Simplified locking - single lock for all operations
        self._lock = asyncio.Lock()
        self._logger = get_logger(f"{__name__}.{db_name}")

        self._logger.info(f"Created enhanced registry for database: {db_name}")

    # =============================================================================
    # INITIALIZATION AND SETUP METHODS
    # =============================================================================

    async def ensure_routes_registered(self):
        """Ensure routes are registered for this database (lazy loading)"""
        # Use new streamlined route manager that works with loaded addons
        await self.route_manager.discover_and_register_routes()
    async def refresh_from_database(self):
        """Refresh registry data from database"""
        try:
            from ..registry.database_registry import DatabaseRegistry
            db_manager = await DatabaseRegistry.get_database(self.db_name)

            # Clear and reload installed modules in installation order
            async with self._lock:
                self.installed_modules.clear()
                self.addon_manager.reset_addon_state()
                self.model_metadata_manager.clear_metadata()
                await self.cache_manager.clear_query_cache()

            # Load modules in installation order (first installed, first loaded)
            modules_query = """
                SELECT name, display_name, version, state, action_at, create_at
                FROM ir_module_module
                WHERE state = 'installed'
                ORDER BY create_at ASC, action_at ASC
            """

            modules = await db_manager.fetch(modules_query)

            addon_order = []
            for module in modules:
                await self.register_module(module['name'], dict(module))
                addon_order.append(module['name'])

            # Set addon load order
            await self.addon_manager.set_addon_load_order(addon_order)

            # Ensure addon paths are registered before loading
            await self._ensure_addon_paths_registered(addon_order)

            # Load models and fields metadata
            await self.model_metadata_manager.load_models_metadata(db_manager)
            await self.model_metadata_manager.load_fields_metadata(db_manager)

            # Load addons in the correct order
            await self.addon_manager.load_addons_in_order()

            # Discover and register routes from loaded addons
            await self.route_manager.discover_and_register_routes()

            self._logger.info(f"Registry refreshed for {self.db_name}: {len(modules)} modules, {len(self.model_metadata_manager.models)} models")

        except Exception as e:
            self._logger.error(f"Failed to refresh registry for {self.db_name}: {e}")

    async def _ensure_addon_paths_registered(self, addon_order: List[str]) -> None:
        """Ensure addon paths are registered with the import system before loading"""
        try:
            from ...addons import _addon_import_manager
            from ...config import config

            # Get addon paths from config
            addon_paths = config.addons_paths

            for addon_name in addon_order:
                # Find the addon in the configured paths
                for addons_path in addon_paths:
                    addon_path = os.path.join(addons_path, addon_name)
                    if os.path.exists(addon_path) and os.path.isdir(addon_path):
                        # Check if it has a manifest file
                        manifest_file = os.path.join(addon_path, '__manifest__.py')
                        if os.path.exists(manifest_file):
                            # Register the addon path
                            _addon_import_manager.register_addon_path(addon_name, addon_path)
                            self._logger.debug(f"Registered addon path for {addon_name}: {addon_path}")
                            break
                else:
                    self._logger.warning(f"Addon path not found for {addon_name}")

        except Exception as e:
            self._logger.error(f"Failed to register addon paths: {e}")

    # =============================================================================
    # ENVIRONMENT MANAGEMENT METHODS
    # =============================================================================

    async def register_environment(self, env):
        """Register an environment with this registry"""
        async with self._lock:
            self.environments.add(env)

    async def unregister_environment(self, env):
        """Unregister an environment from this registry"""
        async with self._lock:
            self.environments.discard(env)

    def get_active_environments_count(self) -> int:
        """Get count of active environments"""
        return len(self.environments)

    # =============================================================================
    # MODULE MANAGEMENT METHODS
    # =============================================================================

    async def register_module(self, module_name: str, module_info: Dict[str, Any]):
        """Register an installed module in the registry"""
        async with self._lock:
            self.installed_modules[module_name] = {
                'name': module_name,
                'display_name': module_info.get('display_name', module_name),
                'version': module_info.get('version', '1.0.0'),
                'state': module_info.get('state', 'installed'),
                'registered_at': time.time()
            }

    async def get_installed_modules(self) -> Dict[str, Dict[str, Any]]:
        """Get all installed modules"""
        async with self._lock:
            return self.installed_modules.copy()

    async def update_registry_after_module_action(self, module_name: str, action: str) -> None:
        """
        Update registry after a module action (install/uninstall/upgrade)
        This method completely reinitializes the AppRegistry

        Args:
            module_name: Name of the module that was acted upon
            action: Action performed (install/uninstall/upgrade)
        """
        try:
            self._logger.info(f"Updating registry after {action} of module '{module_name}' for database '{self.db_name}'")

            # Clear all current state
            async with self._lock:
                self.installed_modules.clear()
                self.addon_manager.reset_addon_state()
                self.model_metadata_manager.clear_metadata()
                await self.cache_manager.clear_query_cache()
                await self.route_manager.clear_routes()

            # Completely reinitialize from database
            await self.refresh_from_database()

            # Ensure routes are registered after reinitialization
            await self.ensure_routes_registered()

            self._logger.info(f"Registry successfully updated after {action} of module '{module_name}' for database '{self.db_name}'")

        except Exception as e:
            self._logger.error(f"Failed to update registry after {action} of module '{module_name}' for database '{self.db_name}': {e}")
            raise

    # =============================================================================
    # ADDON MANAGEMENT METHODS (Delegated to AddonManager)
    # =============================================================================

    async def get_addon_load_order(self) -> List[str]:
        """Get the addon load order"""
        return await self.addon_manager.get_addon_load_order()

    # =============================================================================
    # ROUTE MANAGEMENT METHODS (Delegated to AppRegistryRouteManager)
    # =============================================================================

    async def refresh_routes(self):
        """Refresh routes from loaded addons"""
        await self.route_manager.refresh_routes()

    def get_discovered_routes(self) -> Dict[str, Dict]:
        """Get all discovered routes for this database"""
        return self.route_manager.get_discovered_routes()

    async def clear_routes(self):
        """Clear all routes for this database"""
        await self.route_manager.clear_routes()

    # =============================================================================
    # MODEL METADATA METHODS (Delegated to ModelMetadataManager)
    # =============================================================================

    async def get_model_metadata(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific model"""
        return await self.model_metadata_manager.get_model_metadata(model_name)

    async def get_all_models_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Get metadata for all models"""
        return await self.model_metadata_manager.get_all_models_metadata()

    async def get_model_fields_metadata(self, model_name: str) -> Optional[Dict[str, Dict[str, Any]]]:
        """Get field metadata for a specific model"""
        return await self.model_metadata_manager.get_model_fields_metadata(model_name)

    async def get_field_metadata(self, model_name: str, field_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific field of a model"""
        return await self.model_metadata_manager.get_field_metadata(model_name, field_name)

    async def get_all_fields_metadata(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Get metadata for all fields of all models"""
        return await self.model_metadata_manager.get_all_fields_metadata()

    async def get_model_data(self, model_name: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """Get model data with query cache support"""
        if use_cache:
            cache_key = f"model_data:{model_name}"
            cached_data = await self.cache_manager.get_cached_query(cache_key)
            if cached_data is not None:
                return cached_data

        # Get model data from metadata manager
        model_data = await self.model_metadata_manager.get_model_data(model_name)

        # Cache the result if caching is enabled
        if use_cache and model_data:
            cache_key = f"model_data:{model_name}"
            await self.cache_manager.cache_query(cache_key, model_data)

        return model_data

    async def set_model_data(self, model_name: str, model_data: Dict[str, Any]) -> None:
        """Set model data and update cache"""
        await self.model_metadata_manager.set_model_data(model_name, model_data)
        # Invalidate cache
        await self.cache_manager.invalidate_model_cache(model_name)

    # =============================================================================
    # CACHE MANAGEMENT METHODS (Delegated to CacheManager)
    # =============================================================================

    async def cache_query(self, query_key: str, result: Any) -> None:
        """Cache a query result"""
        await self.cache_manager.cache_query(query_key, result)

    async def get_cached_query(self, query_key: str) -> Optional[Any]:
        """Get a cached query result"""
        return await self.cache_manager.get_cached_query(query_key)

    async def clear_query_cache(self) -> None:
        """Clear the query cache"""
        await self.cache_manager.clear_query_cache()

    async def invalidate_model_cache(self, model_name: str) -> None:
        """Invalidate cache for a specific model"""
        await self.cache_manager.invalidate_model_cache(model_name)

    async def execute_query_cached(self, query_key: str, query_func, *args, **kwargs) -> Any:
        """Execute a query with caching"""
        return await self.cache_manager.execute_query_cached(query_key, query_func, *args, **kwargs)

    # =============================================================================
    # UTILITY AND STATISTICS METHODS
    # =============================================================================

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics"""
        discovered_routes = self.route_manager.get_discovered_routes()
        route_stats = {
            'discovered_count': len(discovered_routes),
            'routes': list(discovered_routes.keys())
        }
        cache_stats = self.cache_manager.get_cache_stats()
        metadata_stats = self.model_metadata_manager.get_metadata_stats()

        return {
            'db_name': self.db_name,
            'created_at': self.created_at,
            'installed_modules_count': len(self.installed_modules),
            'active_environments': self.get_active_environments_count(),
            'uptime_seconds': time.time() - self.created_at,
            'routes': route_stats,
            'cache': cache_stats,
            'metadata': metadata_stats
        }
